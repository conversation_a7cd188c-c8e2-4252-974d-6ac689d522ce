@tailwind base;
@tailwind components;
@tailwind utilities;

/* LifeDonor Global Styles & Theme */
:root {
  --color-primary: #D32F2F;
  --color-primary-dark: #C62828;
  --color-secondary: #FFFFFF;
  --color-text: #333333;
  --color-accent: #1976D2;
  --color-safe: #4CAF50;
  --color-alert: #FFC107;
  --font-family-base: 'Roboto', 'San Francisco', Arial, sans-serif;
  --font-size-base: 16px;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --border-radius: 12px;
  --transition: 0.2s cubic-bezier(0.4,0,0.2,1);
  --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 8px rgba(0,0,0,0.12);
  --shadow-lg: 0 8px 16px rgba(0,0,0,0.15);
}

/* High Contrast Mode */
.high-contrast {
  --color-primary: #000000;
  --color-secondary: #FFFFFF;
  --color-text: #000000;
  --color-accent: #FFD600;
  --color-safe: #00E676;
  --color-alert: #FF3D00;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  color: var(--color-text);
  background: var(--color-secondary);
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  color: var(--color-primary-dark);
  margin-top: 0;
}

button, .btn {
  font-family: inherit;
  font-size: 1rem;
  border-radius: var(--border-radius);
  transition: background var(--transition), color var(--transition);
  cursor: pointer;
  border: none;
  outline: none;
}

input, select, textarea {
  font-family: inherit;
  font-size: 1rem;
  border-radius: var(--border-radius);
  border: 1px solid #ccc;
  padding: 0.75em 1em;
  margin-bottom: 1em;
  transition: border-color var(--transition), box-shadow var(--transition);
}

input:focus, select:focus, textarea:focus {
  border-color: var(--color-accent);
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

/* Accessibility: Large Text */
body.large-text {
  font-size: 1.25rem;
}

body.large-text h1 { font-size: 2.5rem; }
body.large-text h2 { font-size: 2rem; }
body.large-text h3 { font-size: 1.75rem; }
body.large-text h4 { font-size: 1.5rem; }
body.large-text button, body.large-text .btn { font-size: 1.25rem; }
body.large-text input, body.large-text select, body.large-text textarea { font-size: 1.25rem; }

/* Accessibility: Reduce Motion */
.reduce-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

/* Focus Indicators */
button:focus,
input:focus,
select:focus,
textarea:focus,
[tabindex]:focus {
  outline: 3px solid var(--color-accent);
  outline-offset: 2px;
}

/* Screen Reader Only Content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Animation & Transition Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.pulse {
  animation: pulse 1.5s infinite;
}

.bounce {
  animation: bounce 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* Blood Drop Animation */
@keyframes bloodDrop {
  0% { transform: translateY(-10px) scale(0.8); opacity: 0; }
  50% { transform: translateY(0) scale(1); opacity: 1; }
  100% { transform: translateY(10px) scale(0.9); opacity: 0; }
}

.blood-drop {
  animation: bloodDrop 2s infinite;
}

/* Card Hover Effects */
.card-hover {
  transition: transform var(--transition), box-shadow var(--transition);
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(211, 47, 47, 0.15);
}

/* Loading Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
